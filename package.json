{"name": "macro-edge", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite dev", "start": "node build", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "db:generate": "drizzle-kit generate", "db:push": "drizzle-kit push", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "devDependencies": {"@eslint/compat": "^1.2.8", "@eslint/js": "^9.24.0", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/kit": "^2.20.7", "@sveltejs/vite-plugin-svelte": "^5.0.3", "@tailwindcss/vite": "^4.1.4", "@types/sockjs-client": "^1.5.4", "drizzle-kit": "^0.31.0", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-svelte": "^3.5.1", "globals": "^16.0.0", "prettier": "^3.5.3", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.27.0", "svelte-check": "^4.1.6", "tailwindcss": "^4.1.4", "typescript": "^5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.2.6", "vite-plugin-node-polyfills": "^0.23.0"}, "dependencies": {"@better-auth/stripe": "^1.2.7", "@fontsource-variable/inter": "^5.2.5", "@neondatabase/serverless": "^1.0.0", "@upstash/redis": "^1.34.8", "better-auth": "^1.2.7", "chart.js": "^4.4.9", "drizzle-orm": "^0.42.0", "lucide-svelte": "^0.488.0", "openai": "^5.0.1", "posthog-js": "^1.248.0", "resend": "^4.3.0", "sockjs-client": "^1.6.1", "stripe": "^17.7.0", "svelte-sonner": "^1.0.5"}}