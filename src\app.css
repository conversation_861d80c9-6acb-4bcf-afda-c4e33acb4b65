@import "tailwindcss";

::selection {
  color: #fff;
  background-color: var(--color-emerald-700);
}

::-moz-selection {
  color: #fff;
  background-color: var(--color-emerald-700);
}

:root {
  --background: var(--color-gray-900);
  --foreground: var(--color-gray-100);
  --font-sans: "Inter Variable", "sans-serif";
}

body {
  overflow-x: hidden;
  color: var(--foreground);
  background: var(--background);
}

.grecaptcha-badge {
  display: none;
}

.toast {
  z-index: 1;
}

.toast [data-sonner-toaster] {
  font-family: var(--font-sans);
}

.toast [data-sonner-toaster][data-y-position="top"] {
  top: 70px;
}

@media (min-width: 600px) {
  .toast [data-sonner-toaster][data-x-position="left"] {
    left: auto;
  }
}

@media only screen and (max-width: 768px) {
  .demo-only {
    display: none;
  }
}

* {
  scrollbar-width: thin;
  scrollbar-color: oklch(87.2% 0.01 258.338) transparent;
}
*::-webkit-scrollbar {
  width: 15px;
}

*::-webkit-scrollbar-track {
  background: oklch(87.2% 0.01 258.338);
  border-radius: 5px;
}

*::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 14px;
  border: 3px solid oklch(87.2% 0.01 258.338);
}
