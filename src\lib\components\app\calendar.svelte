<script lang="ts">
  let props = $props();
  let groupedItems: [string, CalendarEvent[]][] = $state([]);

  type CalendarEvent = {
    id: string;
    time: number | string | null;
    flag: string | null;
    currency: string | null;
    importance: number | null;
    event: string;
    actual: string | null;
    forecast: string | null;
    previous: string | null;
    actualColor: string | null;
    localDate?: Date | null;
  };

  $effect(() => {
    if (Array.isArray(props.items) && props.items.length > 0) {
      const map = new Map<string, CalendarEvent[]>();

      for (const item of props.items) {
        const key = item.localDate
          ? item.localDate.toLocaleDateString(undefined, {
              weekday: "short",
              day: "2-digit",
              month: "short",
              year: "numeric"
            })
          : "Unknown Date";

        if (!map.has(key)) map.set(key, []);
        map.get(key)!.push(item);
      }

      for (const [key, events] of map.entries()) {
        map.set(
          key,
          events.sort((a, b) => (a.localDate?.getTime() || 0) - (b.localDate?.getTime() || 0))
        );
      }

      groupedItems = Array.from(map.entries()).sort(
        ([a], [b]) => new Date(a).getTime() - new Date(b).getTime()
      );
    }
  });
</script>

<div class="relative w-full tracking-tighter">
  {#if props.items.length === 0 || props.items === null}
    <div class="flex h-[50vh] pt-20">
      <svg
        class="mx-auto mt-6 size-10 animate-spin text-white"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        ><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"
        ></circle><path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path></svg
      >
    </div>
  {:else}
    <div class="relative w-full">
      <table class="w-full table-fixed tracking-tighter">
        <thead class="translate-y-2 text-center text-xs uppercase">
          <tr>
            <th class="w-1/8 truncate p-2 leading-none font-medium md:w-1/6 lg:w-1/8"></th>
            <th class="w-[16px] truncate py-2 leading-none font-medium"></th>
            <th class="w-3/6 truncate p-2 text-left leading-none font-medium"></th>
            <th class="w-1/6 truncate p-2 leading-none font-medium">Actual</th>
            <th class="w-1/6 truncate p-2 leading-none font-medium">Forecast</th>
            <th class="w-1/6 truncate p-2 leading-none font-medium">Previous</th>
            <th class="w-[24px] truncate py-2 leading-none font-medium"></th>
          </tr>
        </thead>
        <tbody class="text-center text-sm">
          {#each groupedItems as [groupDate, groupEvents]}
            <tr>
              <td colspan="7">
                <div
                  class="mt-2 flex w-full justify-between gap-2 rounded-md border-white/5 bg-white/5 px-2 py-2.5 text-left"
                >
                  <h2 class="text-sm leading-none font-medium uppercase">{groupDate}</h2>
                </div>
              </td>
            </tr>

            {#each groupEvents as item}
              <tr>
                <td class="px-2 py-1.5 leading-none font-normal">{item.time}</td>
                <td class="py-1.5 leading-none font-normal">
                  <div class="flex items-center gap-1">
                    <img
                      src="/icons/{item.currency ? item.currency.toUpperCase() : 'white'}.svg"
                      alt={item.currency}
                      class="size-4 rounded-xs select-none"
                      width="16"
                      height="16"
                    />
                  </div>
                </td>
                <td class="truncate px-2 py-1.5 text-left leading-none font-normal">{item.event}</td
                >
                <td
                  class="px-2 py-1.5 leading-none font-normal {item.actualColor == 'greenFont'
                    ? `text-green-500`
                    : item.actualColor == 'redFont'
                      ? `text-red-500`
                      : ``}"
                >
                  {item.actual}
                </td>
                <td class="px-2 py-1.5 leading-none font-normal">{item.forecast}</td>
                <td class="px-2 py-1.5 leading-none font-normal">{item.previous}</td>
                <td class="py-1.5 leading-none font-normal">
                  <div
                    class="inline-block h-3 w-3 rounded-xs {item.actualColor == 'greenFont'
                      ? `bg-green-500`
                      : item.actualColor == 'redFont'
                        ? `bg-red-500`
                        : `bg-gray-300`}"
                  ></div>
                </td>
              </tr>
            {/each}
          {/each}
        </tbody>
      </table>
    </div>
  {/if}
</div>
