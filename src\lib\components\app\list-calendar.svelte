<script lang="ts">
  let { showEarnings = $bindable(), ...props } = $props();
</script>

<div class="relative w-full tracking-tighter">
  <div class="flex w-full justify-between gap-2 rounded-md border-white/5 bg-white/5 px-2 py-2.5">
    <h2 class="text-sm leading-none font-medium uppercase">{props.title}</h2>
    <div class="absolute top-[5px] right-2.5 left-auto flex items-center text-sm leading-none">
      <button
        class={`cursor-pointer rounded-l-md p-1.5 text-center text-xs leading-none font-normal uppercase ring-1 ring-inset ${showEarnings ? "bg-gray-400/10 text-gray-400 ring-gray-400/20" : "bg-green-500/10 text-green-500 ring-green-500/20"}`}
        type="button"
        onclick={() => (showEarnings = false)}>Economic</button
      >
      <button
        class={`cursor-pointer rounded-r-md p-1.5 text-center text-xs leading-none font-normal uppercase ring-1 ring-inset hover:bg-gray-300/10 hover:text-gray-300 hover:ring-gray-300/20 ${showEarnings ? "bg-green-500/10 text-green-500 ring-green-500/20" : "bg-gray-400/10 text-gray-400 ring-gray-400/20"}`}
        type="button"
        onclick={() => (showEarnings = true)}>Earnings</button
      >
    </div>
  </div>
  {#if props?.items?.length === 0 || props?.items === null}
    <svg
      class="mx-auto mt-6 size-10 animate-spin text-white"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      ><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"
      ></circle><path
        class="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      ></path></svg
    >
  {:else}
    <div class="-mx-1.5 w-full overflow-y-auto xl:h-[255px]">
      <table class="w-full table-fixed tracking-tighter">
        <thead class="text-center text-xs uppercase">
          <tr>
            <th class="w-1/8 truncate p-2 leading-none font-medium md:w-1/6 lg:w-1/8"></th>
            <th class="w-[16px] truncate py-2 leading-none font-medium"></th>
            <th class="w-3/6 truncate p-2 text-left leading-none font-medium"></th>
            <th class="w-1/6 truncate p-2 leading-none font-medium">Actual</th>
            <th class="w-1/6 truncate p-2 leading-none font-medium">Forecast</th>
            <th class="w-1/6 truncate p-2 leading-none font-medium">Previous</th>
            <th class="w-[24px] truncate py-2 leading-none font-medium"></th>
          </tr>
        </thead>
        <tbody class="text-center text-sm">
          {#each props.items as item}
            <tr>
              <td class="px-2 py-1.5 leading-none font-normal">{item.time}</td>
              <td class="py-1.5 leading-none font-normal">
                <div class="flex items-center gap-1">
                  <img
                    src="/icons/{item.currency ? item.currency.toUpperCase() : 'white'}.svg"
                    alt={item.flag}
                    class="size-4 rounded-xs select-none"
                    width="16"
                    height="16"
                  />
                  <!-- <p class="hidden sm:inline-block md:hidden lg:inline-block">{item.currency}</p> -->
                </div>
              </td>
              <td class="truncate px-2 py-1.5 text-left leading-none font-normal">{item.event}</td>
              <td
                class="px-2 py-1.5 leading-none font-normal {item.actualColor == 'greenFont'
                  ? `text-green-500`
                  : item.actualColor == 'redFont'
                    ? `text-red-500`
                    : ``}">{item.actual}</td
              >
              <td class="px-2 py-1.5 leading-none font-normal">{item.forecast}</td>
              <td class="px-2 py-1.5 leading-none font-normal">{item.previous}</td>
              <td class="py-1.5 leading-none font-normal">
                {#if item.signal == null || item.signal == 0}
                  <div
                    class="inline-block h-3 w-3 rounded-xs {item.actualColor == 'greenFont'
                      ? `bg-green-500`
                      : item.actualColor == 'redFont'
                        ? `bg-red-500`
                        : `bg-gray-300`}"
                  ></div>
                {:else}
                  <div
                    class="inline-block h-3 w-3 rounded-xs {item.signal > 0
                      ? `bg-green-500`
                      : item.signal < 0
                        ? `bg-red-500`
                        : `bg-gray-300`}"
                  ></div>
                {/if}
              </td>
            </tr>
          {/each}
        </tbody>
      </table>
    </div>
  {/if}
</div>
