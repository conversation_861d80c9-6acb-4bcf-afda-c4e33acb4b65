<script lang="ts">
  let props = $props();
</script>

<div
  class="min-w-[250px] rounded-md bg-gray-800 px-2 py-3 text-left text-sm leading-none tracking-tight whitespace-nowrap shadow-xl"
>
  <div class="flex w-full items-center justify-between gap-1">
    <div class="flex items-center gap-1">
      <img
        src="/icons/{props.currency ? props.currency.toUpperCase() : 'white'}.svg"
        alt={props.flag}
        class="size-4 rounded-xs select-none"
        width="16"
        height="16"
      />
      <p class="max-w-[198px] truncate font-normal">{props.name}</p>
    </div>
    <div
      class="inline-block h-3 w-3 rounded-xs {props.actualColor == 'greenFont'
        ? `bg-green-500`
        : props.actualColor == 'redFont'
          ? `bg-red-500`
          : `bg-gray-300`}"
    ></div>
  </div>
</div>
