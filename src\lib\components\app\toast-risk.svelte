<script lang="ts">
  let props = $props();
  const statusClass = (value: number) =>
    value > 0.002
      ? "bg-green-500/10 text-green-500 ring-green-500/20"
      : value < -0.002
        ? "bg-red-500/10 text-red-500 ring-red-500/20"
        : "bg-white/10 text-white ring-white/20";
  const statusText = (value: number) => (value > 0.002 ? "Buy" : value < -0.002 ? "Sell" : "N/A");
</script>

<div
  class="min-w-[250px] rounded-md bg-gray-800 px-2 py-2.5 text-left text-sm leading-none tracking-tight whitespace-nowrap uppercase shadow-xl"
>
  <div class="flex w-full items-center justify-between gap-1">
    <div class="flex items-center gap-1">
      <img
        src="/icons/{props.logo}"
        alt={props.name}
        class="size-4 rounded-xs select-none"
        width="16"
        height="16"
      />
      <p>{props.name}</p>
    </div>
    <p
      class="rounded-md px-2 py-1 text-center text-xs leading-none font-medium uppercase ring-1 ring-inset {statusClass(
        props.newDay
      )}"
    >
      {statusText(props.newDay)}
    </p>
  </div>
</div>
