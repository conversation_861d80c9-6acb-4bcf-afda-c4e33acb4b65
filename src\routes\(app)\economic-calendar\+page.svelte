<script lang="ts">
  import SockJS from "sockjs-client";
  import { onMount, onDestroy } from "svelte";
  import Calendar from "$lib/components/app/calendar.svelte";

  type CalendarEvent = {
    id: string;
    time: number | string | null;
    flag: string | null;
    currency: string | null;
    importance: number | null;
    event: string;
    actual: string | null;
    forecast: string | null;
    previous: string | null;
    actualColor: string | null;
    localDate?: Date | null;
  };

  let calendarData: CalendarEvent[] = $state([]);
  let calendarIDs: string[] = $state([]);

  let sock: any;
  let TimeZoneID = 55;
  let reconnectInterval = 5000;
  let heartbeatInterval = 25000;
  let isConnected = $state(false);
  let heartbeatTimer: ReturnType<typeof setInterval> | undefined;

  const startSockJS = () => {
    sock = new SockJS("https://streaming.forexpros.com/echo");

    sock.onopen = () => {
      isConnected = true;
      startHeartbeat();

      if (calendarIDs.length === 0) {
        fetchCalendar();
      } else {
        calendarIDs.forEach((id) => {
          if (sock && sock.readyState === SockJS.OPEN) {
            sock.send(
              JSON.stringify({ _event: "subscribe", tzID: TimeZoneID, message: `event-${id}:` })
            );
          }
        });
      }
    };

    sock.onmessage = (e: { data: string }) => {
      try {
        const data = JSON.parse(e.data);

        if (typeof data === "object" && data !== null) {
          data._event = data._event || "tick";
        }

        if (data._event === "tick" && data.message) {
          streamCalendar(data.message.split("::")[1]);
        }
      } catch (err) {}
    };

    sock.onclose = (event: { reason: any; code: number }) => {
      stopHeartbeat();
      isConnected = false;

      if (event.code !== 1000) {
        reconnectSockJS();
      }
    };

    sock.onerror = (error: any) => {
      if (sock) sock.close();
    };
  };

  const startHeartbeat = () => {
    heartbeatTimer = setInterval(() => {
      if (sock && sock.readyState === SockJS.OPEN) {
        sock.send(JSON.stringify({ _event: "heartbeat", data: "h" }));
      }
    }, heartbeatInterval);
  };

  const stopHeartbeat = () => {
    clearInterval(heartbeatTimer);
  };

  const reconnectSockJS = () => {
    stopHeartbeat();
    sock = null;

    setTimeout(() => {
      startSockJS();
    }, reconnectInterval);
  };

  const streamCalendar = (message: any) => {
    const data = JSON.parse(message);

    calendarData = calendarData.map((item: any) =>
      item.id === data.event_ID
        ? {
            ...item,
            actual: data.actual ?? item.actual,
            forecast: data.forecast ?? item.forecast,
            previous: data.previous ?? item.previous,
            actualColor: data.actual_color ?? item.actualColor
          }
        : item
    );
  };

  const fetchCalendar = async () => {
    try {
      const response = await fetch(`/api/calendar-week`);
      const data = await response.json();

      if (response.ok && Array.isArray(data?.data)) {
        const flatEvents: CalendarEvent[] = [];

        for (const day of data.data) {
          const [dd, mm, yyyy] = day.date.split("/");
          const datePart = `${yyyy}-${mm}-${dd}`; // format for Date parsing

          for (const event of day.events) {
            let localDate = null;
            let timeKey = event.time;

            if (event.time && event.time.includes(":")) {
              const [hour, minute] = event.time.split(":").map(Number);
              const utcDate = new Date(
                `${datePart}T${String(hour).padStart(2, "0")}:${String(minute).padStart(2, "0")}:00Z`
              );
              localDate = new Date(utcDate);
              timeKey = localDate.toTimeString().slice(0, 5);
            } else {
              localDate = new Date(`${datePart}T00:00:00Z`);
            }

            flatEvents.push({
              ...event,
              time: timeKey,
              localDate
            });
          }
        }

        if (calendarIDs.length === 0) {
          calendarIDs = flatEvents.map((item: any) => item.id.toString());
          calendarIDs.forEach((id) => {
            if (sock && sock.readyState === SockJS.OPEN) {
              sock.send(
                JSON.stringify({ _event: "subscribe", tzID: TimeZoneID, message: `event-${id}:` })
              );
            }
          });
        }

        calendarData = flatEvents;
      }
    } catch (err) {
      console.error("Error fetching calendar data:", err);
    }
  };

  onMount(() => {
    fetchCalendar();
    startSockJS();
  });

  onDestroy(() => {
    if (sock && (sock.readyState === SockJS.OPEN || sock.readyState === SockJS.CONNECTING)) {
      sock.close(1000, "Component Unmounted");
    }

    stopHeartbeat();
    sock = null;
  });
</script>

<section class="mx-auto max-w-7xl space-y-5 px-2 py-5">
  <h1 class="text-center text-2xl leading-none font-bold tracking-tighter uppercase">
    Economic Calendar
  </h1>

  <div class="relative w-full space-y-2">
    <Calendar items={calendarData} />
  </div>
</section>
